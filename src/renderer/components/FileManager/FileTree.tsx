import React, { useState, useEffect } from 'react';
import { Tree, Dropdown, Modal, Input, message } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { 
  FolderOutlined, 
  FolderOpenOutlined,
  HomeOutlined,
  MoreOutlined,
  FolderAddOutlined,
  DeleteOutlined,
  EditOutlined,
  FileAddOutlined
} from '@ant-design/icons';

interface FileTreeProps {
  currentPath: string;
  onPathChange: (path: string) => void;
  sessionId?: string;
  fileSessionId?: string;
}

interface TreeNode extends DataNode {
  path: string;
  isLeaf?: boolean;
  children?: TreeNode[];
}

const FileTree: React.FC<FileTreeProps> = ({
  currentPath,
  onPathChange,
  sessionId,
  fileSessionId
}) => {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>(['/']);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [loadedKeys, setLoadedKeys] = useState<string[]>([]);
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [contextMenuPath, setContextMenuPath] = useState('');

  useEffect(() => {
    initializeTree();
  }, [sessionId]);

  useEffect(() => {
    // 当前路径改变时，展开到该路径
    expandToPath(currentPath);
  }, [currentPath]);

  const initializeTree = () => {
    const rootNode: TreeNode = {
      title: '根目录',
      key: '/',
      path: '/',
      icon: <HomeOutlined style={{ color: '#52c41a' }} />,
      isLeaf: false,
      children: undefined // 未加载状态
    };

    setTreeData([rootNode]);
    setExpandedKeys(['/']); // 默认展开根目录
    setSelectedKeys([currentPath]);
  };

  const loadChildNodes = async (parentPath: string): Promise<TreeNode[]> => {
    if (loadedKeys.includes(parentPath) || !fileSessionId) {
      return [];
    }

    try {
      const { ipcRenderer } = require('electron');
      const result = await ipcRenderer.invoke('file-list-directory', { 
        fileSessionId, 
        path: parentPath 
      });

      if (!result.success) {
        console.error('加载目录失败:', result.error);
        return [];
      }

      // 只获取目录，过滤掉文件和上级目录
      const directories = result.files.filter((file: any) => 
        file.type === 'directory' && file.name !== '..'
      );

      const childNodes: TreeNode[] = directories.map((dir: any) => ({
        title: dir.name,
        key: dir.path,
        path: dir.path,
        icon: <FolderOutlined style={{ color: '#1890ff' }} />,
        isLeaf: false,
        children: undefined // 未加载状态，需要时动态加载
      }));

      setLoadedKeys(prev => [...prev, parentPath]);
      console.log(`树节点加载完成 ${parentPath}:`, childNodes.length, '个子目录');
      return childNodes;
    } catch (error) {
      console.error('加载目录失败:', error);
      message.error('加载目录失败');
      return [];
    }
  };

  const updateTreeData = (list: TreeNode[], key: string, children: TreeNode[]): TreeNode[] => {
    return list.map(node => {
      if (node.key === key) {
        return { ...node, children };
      }
      if (node.children) {
        return { ...node, children: updateTreeData(node.children, key, children) };
      }
      return node;
    });
  };

  const expandToPath = async (path: string) => {
    if (!path || path === '/') {
      setExpandedKeys(['/']);
      setSelectedKeys(['/']);
      return;
    }

    console.log(`FileTree: 展开到路径 ${path}`);
    
    const pathParts = path.split('/').filter(Boolean);
    const keysToExpand = ['/'];
    
    // 逐级展开并加载
    let buildPath = '';
    for (const part of pathParts) {
      const parentPath = buildPath || '/';
      buildPath = buildPath === '/' ? `/${part}` : `${buildPath}/${part}`;
      keysToExpand.push(buildPath);
      
      // 确保父目录已加载
      if (!loadedKeys.includes(parentPath)) {
        console.log(`FileTree: 加载目录 ${parentPath}`);
        try {
          const children = await loadChildNodes(parentPath);
          setTreeData(prevData => updateTreeData(prevData, parentPath, children));
          setLoadedKeys(prev => [...prev, parentPath]);
        } catch (error) {
          console.error(`FileTree: 加载目录失败 ${parentPath}:`, error);
          break; // 加载失败时停止展开
        }
      }
    }

    console.log(`FileTree: 展开路径列表:`, keysToExpand);
    console.log(`FileTree: 选中路径: ${path}`);
    
    setExpandedKeys(keysToExpand);
    setSelectedKeys([path]);
  };

  const getContextMenuItems = (path: string) => [
    {
      key: 'newFile',
      label: '新建文件',
      icon: <FileAddOutlined />
    },
    {
      key: 'newFolder',
      label: '新建文件夹',
      icon: <FolderAddOutlined />
    },
    {
      key: 'rename',
      label: '重命名',
      icon: <EditOutlined />
    },
    {
      type: 'divider' as const
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true
    }
  ];

  const handleContextMenu = (action: string, path: string) => {
    switch (action) {
      case 'newFile':
        showNewFileModal(path);
        break;
      case 'newFolder':
        showNewFolderModal(path);
        break;
      case 'rename':
        showRenameModal(path);
        break;
      case 'delete':
        showDeleteConfirm(path);
        break;
    }
  };

  const showNewFileModal = (parentPath: string) => {
    let fileName = '';
    
    Modal.confirm({
      title: '新建文件',
      content: (
        <div style={{ marginTop: '16px' }}>
          <div style={{ marginBottom: '8px' }}>
            位置: {parentPath}
          </div>
          <Input
            placeholder="请输入文件名称"
            onChange={(e) => { fileName = e.target.value; }}
            onPressEnter={() => {
              if (fileName.trim()) {
                handleCreateFile(parentPath, fileName.trim());
                Modal.destroyAll();
              }
            }}
            autoFocus
          />
        </div>
      ),
      onOk: () => {
        if (fileName.trim()) {
          handleCreateFile(parentPath, fileName.trim());
        } else {
          message.warning('请输入文件名称');
          return Promise.reject();
        }
      },
      okText: '创建',
      cancelText: '取消'
    });
  };

  const showNewFolderModal = (parentPath: string) => {
    let folderName = '';
    
    Modal.confirm({
      title: '新建文件夹',
      content: (
        <div style={{ marginTop: '16px' }}>
          <div style={{ marginBottom: '8px' }}>
            位置: {parentPath}
          </div>
          <Input
            placeholder="请输入文件夹名称"
            onChange={(e) => { folderName = e.target.value; }}
            onPressEnter={() => {
              if (folderName.trim()) {
                handleCreateFolder(parentPath, folderName.trim());
                Modal.destroyAll();
              }
            }}
            autoFocus
          />
        </div>
      ),
      onOk: () => {
        if (folderName.trim()) {
          handleCreateFolder(parentPath, folderName.trim());
        } else {
          message.warning('请输入文件夹名称');
          return Promise.reject();
        }
      },
      okText: '创建',
      cancelText: '取消'
    });
  };

  const showRenameModal = (filePath: string) => {
    const fileName = filePath.split('/').pop() || '';
    let newName = fileName;
    
    Modal.confirm({
      title: '重命名',
      content: (
        <div style={{ marginTop: '16px' }}>
          <Input
            defaultValue={fileName}
            onChange={(e) => { newName = e.target.value; }}
            onPressEnter={() => {
              if (newName.trim() && newName !== fileName) {
                handleRename(filePath, newName.trim());
                Modal.destroyAll();
              }
            }}
            autoFocus
          />
        </div>
      ),
      onOk: () => {
        if (newName.trim() && newName !== fileName) {
          handleRename(filePath, newName.trim());
        } else {
          return Promise.reject();
        }
      },
      okText: '确定',
      cancelText: '取消'
    });
  };

  const showDeleteConfirm = (filePath: string) => {
    const fileName = filePath.split('/').pop();

    Modal.confirm({
      title: (
        <div style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          ⚠️ 危险操作：确认删除
        </div>
      ),
      content: (
        <div>
          <p style={{ marginBottom: '12px' }}>您即将删除：</p>
          <div style={{
            backgroundColor: '#fff2f0',
            border: '1px solid #ffccc7',
            borderRadius: '4px',
            padding: '8px 12px',
            marginBottom: '12px'
          }}>
            <div style={{
              color: '#cf1322',
              fontFamily: 'monospace'
            }}>
              📄 {fileName}
            </div>
          </div>
          <p style={{
            color: '#ff4d4f',
            fontWeight: 'bold',
            margin: 0
          }}>
            ⚠️ 此操作不可撤销，请谨慎操作！
          </p>
        </div>
      ),
      okType: 'danger',
      okText: '确认删除',
      cancelText: '取消',
      width: 420,
      onOk: () => handleDelete(filePath)
    });
  };

  const handleCreateFile = async (parentPath: string, name: string) => {
    try {
      const { ipcRenderer } = require('electron');
      const filePath = parentPath === '/' ? `/${name}` : `${parentPath}/${name}`;
      
      const result = await ipcRenderer.invoke('file-create-file', {
        fileSessionId,
        path: filePath,
        content: ''
      });

      if (result.success) {
        message.success(`文件 "${name}" 创建成功`);
        // 刷新父目录
        setLoadedKeys(prev => prev.filter(key => key !== parentPath));
        const children = await loadChildNodes(parentPath);
        setTreeData(prevData => updateTreeData(prevData, parentPath, children));
      } else {
        message.error(`创建文件失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      message.error('创建文件失败');
      console.error('创建文件失败:', error);
    }
  };

  const handleCreateFolder = async (parentPath: string, name: string) => {
    try {
      const { ipcRenderer } = require('electron');
      const folderPath = parentPath === '/' ? `/${name}` : `${parentPath}/${name}`;
      
      const result = await ipcRenderer.invoke('file-create-directory', {
        fileSessionId,
        path: folderPath
      });

      if (result.success) {
      message.success(`文件夹 "${name}" 创建成功`);
        // 刷新父目录
      setLoadedKeys(prev => prev.filter(key => key !== parentPath));
        const children = await loadChildNodes(parentPath);
        setTreeData(prevData => updateTreeData(prevData, parentPath, children));
      } else {
        message.error(`创建文件夹失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      message.error('创建文件夹失败');
      console.error('创建文件夹失败:', error);
    }
  };

  const handleRename = async (oldPath: string, newName: string) => {
    try {
      // TODO: 实现SSH重命名
      message.success('重命名成功');
      // 刷新树
      const parentPath = oldPath.split('/').slice(0, -1).join('/') || '/';
      setLoadedKeys(prev => prev.filter(key => key !== parentPath));
      loadChildNodes(parentPath);
    } catch (error) {
      message.error('重命名失败');
    }
  };

  const handleDelete = async (filePath: string) => {
    try {
      // TODO: 实现SSH删除
      message.success('删除成功');
      // 刷新树
      const parentPath = filePath.split('/').slice(0, -1).join('/') || '/';
      setLoadedKeys(prev => prev.filter(key => key !== parentPath));
      loadChildNodes(parentPath);
    } catch (error) {
      message.error('删除失败');
    }
  };

  const onExpand = (keys: React.Key[]) => {
    setExpandedKeys(keys as string[]);
  };

  const onSelect = (keys: React.Key[]) => {
    if (keys.length > 0) {
      const selectedPath = keys[0] as string;
      setSelectedKeys([selectedPath]);
      onPathChange(selectedPath);
    }
  };

  const onRightClick = ({ event, node }: { event: React.MouseEvent; node: any }) => {
    event.preventDefault();
    setContextMenuPath(node.path);
    setContextMenuPosition({ x: event.pageX, y: event.pageY });
    setContextMenuVisible(true);
  };

  // 动态加载树节点
  const onLoadData = (treeNode: any): Promise<void> => {
    return new Promise(async (resolve) => {
      if (treeNode.children) {
        resolve();
        return;
      }

      try {
        const children = await loadChildNodes(treeNode.path);
        setTreeData(prevData => updateTreeData(prevData, treeNode.key, children));
        resolve();
      } catch (error) {
        console.error('动态加载失败:', error);
        resolve();
      }
    });
  };

  return (
    <div style={{ 
      height: '100%', 
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden', // 外层容器不滚动
      backgroundColor: '#fafafa'
    }}>
      <div style={{
        flex: 1,
        overflow: 'auto', // 内层滚动区域
        padding: '8px',
        paddingRight: '4px' // 为滚动条留出空间
      }}>
        <Tree
          treeData={treeData}
          expandedKeys={expandedKeys}
          selectedKeys={selectedKeys}
          onExpand={onExpand}
          onSelect={onSelect}
          loadData={onLoadData}
          onRightClick={onRightClick}
          showIcon
          showLine={true}
          blockNode
          style={{ 
            backgroundColor: 'transparent',
            fontSize: '14px'
          }}
          className="file-tree"
        />
      </div>
      
      {/* 右键菜单 */}
      <Dropdown
        menu={{
          items: getContextMenuItems(contextMenuPath),
          onClick: ({ key }) => {
            handleContextMenu(key, contextMenuPath);
            setContextMenuVisible(false);
          }
        }}
        open={contextMenuVisible}
        onOpenChange={setContextMenuVisible}
      >
        <div
          style={{
            position: 'fixed',
            left: contextMenuPosition.x,
            top: contextMenuPosition.y,
            width: 1,
            height: 1,
            pointerEvents: 'none',
            zIndex: 9999
          }}
        />
      </Dropdown>
      
      <style>{`
        .file-tree .ant-tree-treenode {
          padding: 2px 0;
        }
        
        .file-tree .ant-tree-node-content-wrapper {
          padding: 4px 8px;
          border-radius: 6px;
          transition: all 0.2s;
        }
        
        .file-tree .ant-tree-node-content-wrapper:hover {
          background-color: #f0f7ff;
        }
        
        .file-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
          background-color: #e6f7ff;
          border: 1px solid #91d5ff;
        }
        
        .file-tree .ant-tree-icon {
          margin-right: 8px;
        }
        
        .file-tree .ant-tree-switcher {
          width: 20px;
          height: 20px;
          line-height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .file-tree .ant-tree-title {
          color: #333;
          font-weight: 500;
        }
        
        .file-tree .ant-tree-indent-unit {
          width: 20px;
        }
        
        /* 自定义滚动条样式 */
        .file-tree::-webkit-scrollbar {
          width: 6px;
        }
        
        .file-tree::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        
        .file-tree::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }
        
        .file-tree::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }
      `}</style>
    </div>
  );
};

export default FileTree; 