import React, { useState } from 'react';
import { 
  Table, 
  Card, 
  Tag, 
  Dropdown, 
  Modal, 
  Typography, 
  Space, 
  Button,
  Checkbox,
  Tooltip,
  Avatar,
  Upload,
  Form,
  Select,
  InputNumber,
  Input,
  message
} from 'antd';
import { 
  FileOutlined, 
  FolderOutlined, 
  <PERSON>Outlined,
  DownloadOutlined,
  EditOutlined,
  DeleteOutlined,
  <PERSON>pyOutlined,
  ScissorOutlined,
  EyeOutlined,
  MoreOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileZipOutlined,
  CodeOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FilePptOutlined,
  UploadOutlined,
  FolderOpenOutlined,
  SafetyOutlined,
  BlockOutlined,
  FileAddOutlined,
  FolderAddOutlined
} from '@ant-design/icons';
import { FileItem } from './FileManager';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import type { ColumnsType, TableRowSelection } from 'antd/es/table/interface';

const { Text } = Typography;

interface FileListProps {
  files: FileItem[];
  selectedFiles: string[];
  currentPath: string;
  onFileSelect: (selectedFiles: string[]) => void;
  onFileDoubleClick: (file: FileItem) => void;
  onContextMenu: (file: FileItem, selectedFiles: FileItem[]) => void;
  clipboard?: {files: FileItem[], operation: 'copy' | 'cut'} | null;
  onClipboardChange?: (clipboard: {files: FileItem[], operation: 'copy' | 'cut'} | null) => void;
  fileSessionId?: string;
  onRefresh?: () => void;
}

const FileList: React.FC<FileListProps> = ({
  files,
  selectedFiles,
  currentPath: propCurrentPath,
  onFileSelect,
  onFileDoubleClick,
  onContextMenu,
  clipboard: externalClipboard,
  onClipboardChange,
  fileSessionId,
  onRefresh
}) => {
  const [sortedInfo, setSortedInfo] = useState<any>({});
  
  // 模态框状态
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [renameModalVisible, setRenameModalVisible] = useState(false);
  const [moveModalVisible, setMoveModalVisible] = useState(false);
  const [permissionsModalVisible, setPermissionsModalVisible] = useState(false);
  const [newFileModalVisible, setNewFileModalVisible] = useState(false);
  const [newFolderModalVisible, setNewFolderModalVisible] = useState(false);
  
  // 当前操作的文件
  const [currentFile, setCurrentFile] = useState<FileItem | null>(null);
  const [newFileName, setNewFileName] = useState('');
  const [newFolderName, setNewFolderName] = useState('');
  const [targetPath, setTargetPath] = useState('');
  const [currentPath, setCurrentPath] = useState('/'); // 操作时的临时路径
  
  // 右键菜单状态
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [contextMenuFile, setContextMenuFile] = useState<FileItem | null>(null);
  const [isEmptyAreaMenu, setIsEmptyAreaMenu] = useState(false);
  
  // 使用外部剪贴板状态，如果没有传入则使用本地状态作为fallback
  const [localClipboard, setLocalClipboard] = useState<{files: FileItem[], operation: 'copy' | 'cut'} | null>(null);
  const clipboard = externalClipboard !== undefined ? externalClipboard : localClipboard;
  const setClipboard = onClipboardChange || setLocalClipboard;
  
  // 权限修改状态
  const [permissions, setPermissions] = useState({
    owner: { read: true, write: true, execute: true },
    group: { read: true, write: false, execute: true },
    other: { read: true, write: false, execute: false }
  });

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'directory') {
      return <FolderOutlined style={{ fontSize: '16px', color: '#1890ff' }} />;
    }
    if (file.type === 'symlink') {
      return <LinkOutlined style={{ fontSize: '16px', color: '#722ed1' }} />;
    }

    // 根据文件扩展名返回不同图标
    const extension = file.name.split('.').pop()?.toLowerCase();
    const iconStyle = { fontSize: '16px' };

    switch (extension) {
      case 'pdf':
        return <FilePdfOutlined style={{ ...iconStyle, color: '#ff4d4f' }} />;
      case 'doc':
      case 'docx':
        return <FileWordOutlined style={{ ...iconStyle, color: '#1890ff' }} />;
      case 'xls':
      case 'xlsx':
        return <FileExcelOutlined style={{ ...iconStyle, color: '#52c41a' }} />;
      case 'ppt':
      case 'pptx':
        return <FilePptOutlined style={{ ...iconStyle, color: '#fa8c16' }} />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return <FileImageOutlined style={{ ...iconStyle, color: '#eb2f96' }} />;
      case 'zip':
      case 'rar':
      case 'tar':
      case 'gz':
      case '7z':
        return <FileZipOutlined style={{ ...iconStyle, color: '#722ed1' }} />;
      case 'js':
      case 'ts':
      case 'jsx':
      case 'tsx':
      case 'py':
      case 'java':
      case 'cpp':
      case 'c':
      case 'php':
      case 'rb':
      case 'go':
        return <CodeOutlined style={{ ...iconStyle, color: '#13c2c2' }} />;
      case 'txt':
      case 'md':
      case 'log':
        return <FileTextOutlined style={{ ...iconStyle, color: '#595959' }} />;
      default:
        return <FileOutlined style={{ ...iconStyle, color: '#595959' }} />;
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '-';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  const formatPermissions = (permissions?: string) => {
    if (!permissions) return '-';
    
    const typeChar = permissions[0];
    const ownerPerms = permissions.slice(1, 4);
    const groupPerms = permissions.slice(4, 7);
    const otherPerms = permissions.slice(7, 10);
    
    let typeText = '';
    let typeColor = 'default';
    switch (typeChar) {
      case 'd': 
        typeText = '目录'; 
        typeColor = 'blue';
        break;
      case 'l': 
        typeText = '链接'; 
        typeColor = 'purple';
        break;
      case '-': 
        typeText = '文件'; 
        typeColor = 'default';
        break;
      default: 
        typeText = '其他';
        typeColor = 'orange';
    }
    
    return (
      <Space size={4}>
        <Tag color={typeColor}>
          {typeText}
        </Tag>
        <Text code style={{ fontSize: '10px' }}>
          {ownerPerms}-{groupPerms}-{otherPerms}
        </Text>
      </Space>
    );
  };

  const getContextMenuItems = (file: FileItem) => {
    const isDirectory = file.type === 'directory';
    const isParentDir = file.name === '..';
    
    if (isParentDir) return [];
    
    const items = [
      {
        key: 'open',
        label: isDirectory ? '打开' : '查看',
        icon: <EyeOutlined />
      }
    ];
    
    if (!isDirectory) {
      items.push({
        key: 'download',
        label: '下载',
        icon: <DownloadOutlined />
      });
    }
    
    // 如果是目录，添加新建和上传选项
    if (isDirectory) {
      items.push(
        {
          key: 'newFile',
          label: '新建文件',
          icon: <FileAddOutlined />
        },
        {
          key: 'newFolder',
          label: '新建文件夹',
          icon: <FolderAddOutlined />
        },
        {
        key: 'upload',
        label: '上传文件',
        icon: <UploadOutlined />
        }
      );
    }
    
    items.push(
      {
        key: 'divider1',
        type: 'divider' as const
      },
      {
        key: 'copy',
        label: '复制',
        icon: <CopyOutlined />
      },
      {
        key: 'cut',
        label: '剪切',
        icon: <ScissorOutlined />
      },
      {
        key: 'move',
        label: '移动',
        icon: <BlockOutlined />
      },
      {
        key: 'rename',
        label: '重命名',
        icon: <EditOutlined />
      },
      {
        key: 'divider2',
        type: 'divider' as const
      },
      {
        key: 'permissions',
        label: '修改权限',
        icon: <SafetyOutlined />
      },
      {
        key: 'divider3',
        type: 'divider' as const
      },
      {
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true
      }
    );
    
    return items;
  };

  // 空白区域右键菜单
  const getEmptyAreaContextMenuItems = () => [
    {
      key: 'newFile',
      label: '新建文件',
      icon: <FileAddOutlined />
    },
    {
      key: 'newFolder',
      label: '新建文件夹',
      icon: <FolderAddOutlined />
    },
    {
      type: 'divider' as const
    },
    {
      key: 'refresh',
      label: '刷新',
      icon: <FolderOpenOutlined />
    }
  ];

  const handleContextMenuClick = (key: string, file?: FileItem) => {
    switch (key) {
      case 'open':
        if (file) onFileDoubleClick(file);
        break;
      case 'upload':
        if (file) handleUpload(file);
        break;
      case 'download':
        if (file) handleDownload(file);
        break;
      case 'copy':
        if (file) handleCopy([file]);
        break;
      case 'cut':
        if (file) handleCut([file]);
        break;
      case 'move':
        if (file) handleMove([file]);
        break;
      case 'rename':
        if (file) handleRename(file);
        break;
      case 'permissions':
        if (file) handlePermissions(file);
        break;
      case 'delete':
        if (file) handleDelete([file]);
        break;
      case 'newFile':
        handleNewFile(file?.type === 'directory' ? file.path : propCurrentPath);
        break;
      case 'newFolder':
        handleNewFolder(file?.type === 'directory' ? file.path : propCurrentPath);
        break;
      case 'refresh':
        if (onRefresh) onRefresh();
        break;
    }
  };

  // 上传文件
  const handleUpload = (file: FileItem) => {
    setCurrentFile(file);
    setUploadModalVisible(true);
  };

  // 下载文件
  const handleDownload = (file: FileItem) => {
    message.info(`开始下载文件: ${file.name}`);
    // TODO: 实现真实的文件下载逻辑
    console.log('下载文件:', file);
  };

  // 复制文件
  const handleCopy = (files: FileItem[]) => {
    setClipboard({ files, operation: 'copy' });
    message.success(`已复制 ${files.length} 个项目到剪贴板`);
  };

  // 剪切文件
  const handleCut = (files: FileItem[]) => {
    setClipboard({ files, operation: 'cut' });
    message.success(`已剪切 ${files.length} 个项目到剪贴板`);
  };

  // 移动文件
  const handleMove = (files: FileItem[]) => {
    setCurrentFile(files[0]);
    setTargetPath('');
    setMoveModalVisible(true);
  };

  // 重命名文件
  const handleRename = (file: FileItem) => {
    setCurrentFile(file);
    setNewFileName(file.name);
    setRenameModalVisible(true);
  };

  // 修改权限
  const handlePermissions = (file: FileItem) => {
    setCurrentFile(file);
    // 解析当前权限
    if (file.permissions) {
      const perms = file.permissions;
      setPermissions({
        owner: {
          read: perms.charAt(1) === 'r',
          write: perms.charAt(2) === 'w',
          execute: perms.charAt(3) === 'x'
        },
        group: {
          read: perms.charAt(4) === 'r',
          write: perms.charAt(5) === 'w',
          execute: perms.charAt(6) === 'x'
        },
        other: {
          read: perms.charAt(7) === 'r',
          write: perms.charAt(8) === 'w',
          execute: perms.charAt(9) === 'x'
        }
      });
    }
    setPermissionsModalVisible(true);
  };

  // 新建文件
  const handleNewFile = (parentPath: string) => {
    setCurrentPath(parentPath);
    setNewFileName('');
    setNewFileModalVisible(true);
  };

  // 新建文件夹
  const handleNewFolder = (parentPath: string) => {
    setCurrentPath(parentPath);
    setNewFolderName('');
    setNewFolderModalVisible(true);
  };

  // 创建文件
  const handleCreateFile = async () => {
    if (!newFileName.trim()) {
      message.warning('请输入文件名称');
      return;
    }

    try {
      const { ipcRenderer } = require('electron');
      const filePath = currentPath === '/' ? `/${newFileName}` : `${currentPath}/${newFileName}`;
      
      const result = await ipcRenderer.invoke('file-create-file', {
        fileSessionId,
        path: filePath,
        content: ''
      });

      if (result.success) {
        message.success(`文件 "${newFileName}" 创建成功`);
        setNewFileModalVisible(false);
        if (onRefresh) onRefresh();
      } else {
        message.error(`创建文件失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      message.error('创建文件失败');
      console.error('创建文件失败:', error);
    }
  };

  // 创建文件夹
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      message.warning('请输入文件夹名称');
      return;
    }

    try {
      const { ipcRenderer } = require('electron');
      const folderPath = currentPath === '/' ? `/${newFolderName}` : `${currentPath}/${newFolderName}`;
      
      const result = await ipcRenderer.invoke('file-create-directory', {
        fileSessionId,
        path: folderPath
      });

      if (result.success) {
        message.success(`文件夹 "${newFolderName}" 创建成功`);
        setNewFolderModalVisible(false);
        if (onRefresh) onRefresh();
      } else {
        message.error(`创建文件夹失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      message.error('创建文件夹失败');
      console.error('创建文件夹失败:', error);
    }
  };

  const handleDelete = (files: FileItem[]) => {
    const fileNames = files.map(f => f.name);
    const isMultiple = files.length > 1;

    Modal.confirm({
      title: (
        <div style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          ⚠️ 危险操作：确认删除
        </div>
      ),
      content: (
        <div>
          <p style={{ marginBottom: '12px' }}>
            {isMultiple
              ? `您即将删除以下 ${files.length} 个项目：`
              : `您即将删除：`
            }
          </p>
          <div style={{
            backgroundColor: '#fff2f0',
            border: '1px solid #ffccc7',
            borderRadius: '4px',
            padding: '8px 12px',
            marginBottom: '12px',
            maxHeight: '120px',
            overflowY: 'auto'
          }}>
            {fileNames.map((name, index) => (
              <div key={index} style={{
                padding: '2px 0',
                color: '#cf1322',
                fontFamily: 'monospace'
              }}>
                📄 {name}
              </div>
            ))}
          </div>
          <p style={{
            color: '#ff4d4f',
            fontWeight: 'bold',
            margin: 0
          }}>
            ⚠️ 此操作不可撤销，请谨慎操作！
          </p>
        </div>
      ),
      okType: 'danger',
      okText: '确认删除',
      cancelText: '取消',
      width: 480,
      onOk: async () => {
        try {
          const { ipcRenderer } = require('electron');

          for (const file of files) {
            const result = await ipcRenderer.invoke('file-delete', {
              fileSessionId: fileSessionId,
              path: file.path
            });

            if (!result.success) {
              message.error(`删除 "${file.name}" 失败: ${result.error || '未知错误'}`);
              return;
            }
          }

          message.success(`成功删除 ${files.length} 个项目`);

          // 刷新文件列表
          if (onRefresh) {
            onRefresh();
          }
        } catch (error) {
          message.error(`删除失败: ${error.message}`);
        }
      }
    });
  };

  // 表格行选择配置
  const rowSelection: TableRowSelection<FileItem> = {
    selectedRowKeys: selectedFiles,
    onChange: (selectedRowKeys: React.Key[]) => {
      onFileSelect(selectedRowKeys as string[]);
    },
    getCheckboxProps: (record: FileItem) => ({
      disabled: record.name === '..' // 禁用父目录的选择框
    }),
  };

  // 表格列配置
  const columns: ColumnsType<FileItem> = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      sorter: (a, b) => a.name.localeCompare(b.name),
      sortOrder: sortedInfo.columnKey === 'name' ? sortedInfo.order : null,
      render: (name: string, record: FileItem) => (
        <Space size={8}>
          {getFileIcon(record)}
          <span>
            <Text strong={record.type === 'directory'} style={{ fontSize: '13px' }}>
              {name}
                </Text>
                         {record.type === 'directory' && name !== '..' && (
               <Tag color="blue" style={{ marginLeft: '6px', fontSize: '10px' }}>
                    文件夹
                  </Tag>
                )}
             {record.type === 'symlink' && (
               <Tag color="purple" style={{ marginLeft: '6px', fontSize: '10px' }}>
                    链接
                  </Tag>
                )}
          </span>
        </Space>
      ),
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      align: 'right',
      sorter: (a, b) => (a.size || 0) - (b.size || 0),
      sortOrder: sortedInfo.columnKey === 'size' ? sortedInfo.order : null,
      render: (size: number, record: FileItem) => (
        <Text type="secondary" style={{ fontSize: '11px' }}>
          {record.type === 'directory' ? '-' : formatFileSize(size)}
        </Text>
      ),
    },
    {
      title: '修改时间',
      dataIndex: 'modified',
      key: 'modified',
      width: 140,
      sorter: (a, b) => {
        const timeA = a.modified ? new Date(a.modified).getTime() : 0;
        const timeB = b.modified ? new Date(b.modified).getTime() : 0;
        return timeA - timeB;
      },
      sortOrder: sortedInfo.columnKey === 'modified' ? sortedInfo.order : null,
      render: (modified: Date) => (
        <Text type="secondary" style={{ fontSize: '11px' }}>
          {modified ? formatDistanceToNow(modified, { addSuffix: true, locale: zhCN }) : '-'}
        </Text>
      ),
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 120,
      render: (permissions: string) => formatPermissions(permissions),
    },

  ];

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setSortedInfo(sorter);
  };

  return (
    <div style={{ 
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      padding: '4px'
    }}>
      {/* 批量操作栏 */}
      {selectedFiles.length > 0 && (
        <Card 
          size="small" 
          style={{ 
            marginBottom: '6px',
            backgroundColor: '#f6ffed',
            borderColor: '#b7eb8f'
          }}
          styles={{ body: { padding: '8px 12px' } }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text style={{ fontSize: '11px' }}>
                已选择 {selectedFiles.length} 个项目
              </Text>
              <Space size="small">
                <Button 
                  size="small" 
                  type="primary" 
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    const selectedFileObjects = files.filter(f => selectedFiles.includes(f.path));
                    selectedFileObjects.forEach(file => handleDownload(file));
                  }}
                >
                  下载
                </Button>
                <Button 
                  size="small" 
                  icon={<CopyOutlined />}
                  onClick={() => {
                    const selectedFileObjects = files.filter(f => selectedFiles.includes(f.path));
                    handleCopy(selectedFileObjects);
                  }}
                >
                  复制
                </Button>
                <Button 
                  size="small" 
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    const selectedFileObjects = files.filter(f => selectedFiles.includes(f.path));
                    handleDelete(selectedFileObjects);
                  }}
                >
                  删除
                </Button>
                <Button 
                  size="small" 
                  type="text"
                  onClick={() => onFileSelect([])}
                >
                  取消选择
                </Button>
              </Space>
          </div>
        </Card>
      )}

      {/* 文件表格 */}
      <div
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          backgroundColor: '#fff'
        }}
        onContextMenu={(e) => {
          // 空白区域右键菜单
          e.preventDefault();
          setContextMenuFile(null);
          setIsEmptyAreaMenu(true);
          setContextMenuPosition({ x: e.pageX, y: e.pageY });
          setContextMenuVisible(true);
        }}
      >
        <Table<FileItem>
          columns={columns}
          dataSource={files}
          rowKey="path"
          rowSelection={rowSelection}
          pagination={false}
          scroll={{ y: '100%' }}
          size="small"
          onChange={handleTableChange}
          style={{
            flex: 1,
            minHeight: '200px'
          }}
          onRow={(record) => ({
            onDoubleClick: () => onFileDoubleClick(record),
            onContextMenu: (e) => {
              e.preventDefault();
              setContextMenuFile(record);
              setIsEmptyAreaMenu(false);
              setContextMenuPosition({ x: e.pageX, y: e.pageY });
              setContextMenuVisible(true);
            },
            style: { cursor: 'pointer' }
          })}
          locale={{
            emptyText: (
              <div
                style={{
                  padding: '40px 0',
                  color: '#999',
                  textAlign: 'center',
                  minHeight: '200px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
                onContextMenu={(e) => {
                  e.preventDefault();
                  setContextMenuFile(null);
                  setIsEmptyAreaMenu(true);
                  setContextMenuPosition({ x: e.pageX, y: e.pageY });
                  setContextMenuVisible(true);
                }}
              >
                <FolderOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>此目录为空</div>
              </div>
            )
          }}
        />
        {/* 空白填充区域，确保右键菜单在整个区域都能工作 */}
        <div
          style={{
            flex: 1,
            minHeight: '50px',
            backgroundColor: '#fafafa'
          }}
          onContextMenu={(e) => {
            e.preventDefault();
            setContextMenuFile(null);
            setIsEmptyAreaMenu(true);
            setContextMenuPosition({ x: e.pageX, y: e.pageY });
            setContextMenuVisible(true);
          }}
        />
      </div>

      {/* 右键菜单 */}
      <Dropdown
        menu={{
          items: isEmptyAreaMenu ? getEmptyAreaContextMenuItems() : (contextMenuFile ? getContextMenuItems(contextMenuFile) : []),
          onClick: ({ key }) => {
            handleContextMenuClick(key, contextMenuFile || undefined);
            setContextMenuVisible(false);
          }
        }}
        open={contextMenuVisible}
        onOpenChange={setContextMenuVisible}
      >
        <div
          style={{
            position: 'fixed',
            left: contextMenuPosition.x,
            top: contextMenuPosition.y,
            width: 1,
            height: 1,
            pointerEvents: 'none',
            zIndex: 9999
          }}
        />
      </Dropdown>

      {/* 上传文件模态框 */}
      <Modal
        title="上传文件"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          name="file"
          multiple
          beforeUpload={(file) => {
            message.info(`准备上传: ${file.name}`);
            // TODO: 实现文件上传逻辑
            return false; // 阻止默认上传
          }}
        >
          <p><UploadOutlined style={{ fontSize: '48px', color: '#1890ff' }} /></p>
          <p>点击或拖拽文件到此区域上传</p>
          <p style={{ color: '#666' }}>支持单个或批量上传</p>
        </Upload.Dragger>
      </Modal>

      {/* 重命名模态框 */}
      <Modal
        title="重命名"
        open={renameModalVisible}
        onOk={async () => {
          if (currentFile && newFileName.trim() && newFileName !== currentFile.name) {
            try {
              const { ipcRenderer } = require('electron');
              
              // 改进路径计算逻辑
              const pathParts = currentFile.path.split('/');
              pathParts.pop(); // 移除文件名
              const parentPath = pathParts.join('/') || '/';
              const newPath = parentPath === '/' ? `/${newFileName}` : `${parentPath}/${newFileName}`;
              
              console.log('重命名操作:', {
                oldPath: currentFile.path,
                newPath: newPath,
                fileSessionId: fileSessionId
              });
              
              const result = await ipcRenderer.invoke('file-rename', {
                fileSessionId: fileSessionId,
                oldPath: currentFile.path,
                newPath: newPath
              });

              if (result.success) {
                message.success(`文件 "${currentFile.name}" 已重命名为 "${newFileName}"`);
                setRenameModalVisible(false);
                // 刷新文件列表
                if (onRefresh) {
                  onRefresh();
                }
              } else {
                message.error(`重命名失败: ${result.error || '未知错误'}`);
              }
            } catch (error) {
              message.error(`重命名失败: ${error.message}`);
            }
          } else if (newFileName === currentFile?.name) {
            message.info('文件名没有改变');
            setRenameModalVisible(false);
          }
        }}
        onCancel={() => setRenameModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Input
          value={newFileName}
          onChange={(e) => setNewFileName(e.target.value)}
          placeholder="请输入新文件名"
          onPressEnter={async () => {
            if (currentFile && newFileName.trim() && newFileName !== currentFile.name) {
                             try {
                 const { ipcRenderer } = require('electron');
                 
                 // 改进路径计算逻辑
                 const pathParts = currentFile.path.split('/');
                 pathParts.pop(); // 移除文件名
                 const parentPath = pathParts.join('/') || '/';
                 const newPath = parentPath === '/' ? `/${newFileName}` : `${parentPath}/${newFileName}`;
                 
                 const result = await ipcRenderer.invoke('file-rename', {
                   fileSessionId: fileSessionId,
                   oldPath: currentFile.path,
                   newPath: newPath
                 });

                if (result.success) {
                  message.success(`文件 "${currentFile.name}" 已重命名为 "${newFileName}"`);
                  setRenameModalVisible(false);
                  if (onRefresh) {
                    onRefresh();
                  }
                } else {
                  message.error(`重命名失败: ${result.error || '未知错误'}`);
                }
              } catch (error) {
                message.error(`重命名失败: ${error.message}`);
              }
            }
          }}
        />
      </Modal>

      {/* 移动文件模态框 */}
      <Modal
        title="移动文件"
        open={moveModalVisible}
        onOk={async () => {
          if (currentFile && targetPath.trim()) {
            try {
              const { ipcRenderer } = require('electron');
              const fileName = currentFile.name;
              const newPath = targetPath.endsWith('/') ? `${targetPath}${fileName}` : `${targetPath}/${fileName}`;
              
              const result = await ipcRenderer.invoke('file-rename', {
                fileSessionId: fileSessionId,
                oldPath: currentFile.path,
                newPath: newPath
              });

              if (result.success) {
                message.success(`文件 "${currentFile.name}" 已移动到 "${targetPath}"`);
                setMoveModalVisible(false);
                
                // 刷新文件列表
                if (onRefresh) {
                  onRefresh();
                }
              } else {
                message.error(`移动失败: ${result.error || '未知错误'}`);
              }
            } catch (error) {
              message.error(`移动失败: ${error.message}`);
            }
          }
        }}
        onCancel={() => setMoveModalVisible(false)}
        okText="移动"
        cancelText="取消"
      >
        <Input
          value={targetPath}
          onChange={(e) => setTargetPath(e.target.value)}
          placeholder="请输入目标路径 (如: /home/<USER>/documents)"
          addonBefore="目标路径:"
        />
      </Modal>

      {/* 修改权限模态框 */}
      <Modal
        title="修改权限"
        open={permissionsModalVisible}
        onOk={async () => {
          if (currentFile) {
            try {
              const { ipcRenderer } = require('electron');
              
              // 计算八进制权限值
              const ownerPerm = (permissions.owner.read ? 4 : 0) + 
                               (permissions.owner.write ? 2 : 0) + 
                               (permissions.owner.execute ? 1 : 0);
              const groupPerm = (permissions.group.read ? 4 : 0) + 
                               (permissions.group.write ? 2 : 0) + 
                               (permissions.group.execute ? 1 : 0);
              const otherPerm = (permissions.other.read ? 4 : 0) + 
                               (permissions.other.write ? 2 : 0) + 
                               (permissions.other.execute ? 1 : 0);
              
              const octalPermissions = `${ownerPerm}${groupPerm}${otherPerm}`;
              
              const result = await ipcRenderer.invoke('file-change-permissions', {
                fileSessionId: fileSessionId,
                path: currentFile.path,
                permissions: octalPermissions
              });

              if (result.success) {
                const newPerms = [
                  currentFile.type === 'directory' ? 'd' : '-',
                  permissions.owner.read ? 'r' : '-',
                  permissions.owner.write ? 'w' : '-',
                  permissions.owner.execute ? 'x' : '-',
                  permissions.group.read ? 'r' : '-',
                  permissions.group.write ? 'w' : '-',
                  permissions.group.execute ? 'x' : '-',
                  permissions.other.read ? 'r' : '-',
                  permissions.other.write ? 'w' : '-',
                  permissions.other.execute ? 'x' : '-'
                ].join('');
                
                message.success(`已修改 "${currentFile.name}" 的权限为 ${newPerms}`);
                setPermissionsModalVisible(false);
                
                // 刷新文件列表
                if (onRefresh) {
                  onRefresh();
                }
              } else {
                message.error(`修改权限失败: ${result.error || '未知错误'}`);
              }
            } catch (error) {
              message.error(`修改权限失败: ${error.message}`);
            }
          }
        }}
        onCancel={() => setPermissionsModalVisible(false)}
        okText="确定"
        cancelText="取消"
        width={600}
      >
        <Form layout="vertical">
          <Form.Item label="所有者权限">
            <Space>
              <Checkbox
                checked={permissions.owner.read}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  owner: { ...prev.owner, read: e.target.checked }
                }))}
              >
                读取 (r)
              </Checkbox>
              <Checkbox
                checked={permissions.owner.write}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  owner: { ...prev.owner, write: e.target.checked }
                }))}
              >
                写入 (w)
              </Checkbox>
              <Checkbox
                checked={permissions.owner.execute}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  owner: { ...prev.owner, execute: e.target.checked }
                }))}
              >
                执行 (x)
              </Checkbox>
            </Space>
          </Form.Item>
          
          <Form.Item label="群组权限">
            <Space>
              <Checkbox
                checked={permissions.group.read}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  group: { ...prev.group, read: e.target.checked }
                }))}
              >
                读取 (r)
              </Checkbox>
              <Checkbox
                checked={permissions.group.write}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  group: { ...prev.group, write: e.target.checked }
                }))}
              >
                写入 (w)
              </Checkbox>
              <Checkbox
                checked={permissions.group.execute}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  group: { ...prev.group, execute: e.target.checked }
                }))}
              >
                执行 (x)
              </Checkbox>
            </Space>
          </Form.Item>
          
          <Form.Item label="其他用户权限">
            <Space>
              <Checkbox
                checked={permissions.other.read}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  other: { ...prev.other, read: e.target.checked }
                }))}
              >
                读取 (r)
              </Checkbox>
              <Checkbox
                checked={permissions.other.write}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  other: { ...prev.other, write: e.target.checked }
                }))}
              >
                写入 (w)
              </Checkbox>
              <Checkbox
                checked={permissions.other.execute}
                onChange={(e) => setPermissions(prev => ({
                  ...prev,
                  other: { ...prev.other, execute: e.target.checked }
                }))}
              >
                执行 (x)
              </Checkbox>
            </Space>
          </Form.Item>
          
          <Form.Item label="权限预览">
            <Text code>
              {[
                currentFile?.type === 'directory' ? 'd' : '-',
                permissions.owner.read ? 'r' : '-',
                permissions.owner.write ? 'w' : '-',
                permissions.owner.execute ? 'x' : '-',
                permissions.group.read ? 'r' : '-',
                permissions.group.write ? 'w' : '-',
                permissions.group.execute ? 'x' : '-',
                permissions.other.read ? 'r' : '-',
                permissions.other.write ? 'w' : '-',
                permissions.other.execute ? 'x' : '-'
              ].join('')}
            </Text>
          </Form.Item>
        </Form>
      </Modal>

      {/* 新建文件模态框 */}
      <Modal
        title="新建文件"
        open={newFileModalVisible}
        onOk={handleCreateFile}
        onCancel={() => setNewFileModalVisible(false)}
        okText="创建"
        cancelText="取消"
      >
        <Input
          value={newFileName}
          onChange={(e) => setNewFileName(e.target.value)}
          placeholder="请输入文件名 (例如: myfile.txt)"
          onPressEnter={handleCreateFile}
        />
      </Modal>

      {/* 新建文件夹模态框 */}
      <Modal
        title="新建文件夹"
        open={newFolderModalVisible}
        onOk={handleCreateFolder}
        onCancel={() => setNewFolderModalVisible(false)}
        okText="创建"
        cancelText="取消"
      >
        <Input
          value={newFolderName}
          onChange={(e) => setNewFolderName(e.target.value)}
          placeholder="请输入文件夹名 (例如: myfolder)"
          onPressEnter={handleCreateFolder}
        />
      </Modal>
    </div>
  );
};

export default FileList; 